# Simple 4inWar Game Launcher
# Fixed version to avoid path issues

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "4inWar Simple Game Launcher" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan

# Configuration
$PythonExe = "D:\SoftWare\anaconda3\envs\My_ENV\python.exe"
$GameDir = "d:\Y2K\Cloud\MyCloud\Code\Python\Projects\4inWar"

# Check if paths exist
Write-Host "Checking environment..." -ForegroundColor Yellow

if (-not (Test-Path $PythonExe)) {
    Write-Host "ERROR: Python not found at: $PythonExe" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

if (-not (Test-Path $GameDir)) {
    Write-Host "ERROR: Game directory not found at: $GameDir" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

if (-not (Test-Path "$GameDir\server\main.py")) {
    Write-Host "ERROR: Server script not found at: $GameDir\server\main.py" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

if (-not (Test-Path "$GameDir\client\main.py")) {
    Write-Host "ERROR: Client script not found at: $GameDir\client\main.py" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host "Environment check passed!" -ForegroundColor Green

# Change to game directory
Set-Location $GameDir
Write-Host "Changed to directory: $(Get-Location)" -ForegroundColor Green

# Start server
Write-Host "Starting server..." -ForegroundColor Green
try {
    $ServerProcess = Start-Process -FilePath $PythonExe -ArgumentList "server\main.py" -WindowStyle Normal -PassThru
    Write-Host "Server started (PID: $($ServerProcess.Id))" -ForegroundColor Green
} catch {
    Write-Host "Failed to start server: $($_.Exception.Message)" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

# Wait for server
Write-Host "Waiting for server to initialize (3 seconds)..." -ForegroundColor Yellow
Start-Sleep -Seconds 3

# Start clients
$ClientProcesses = @()
for ($i = 1; $i -le 4; $i++) {
    Write-Host "Starting client $i..." -ForegroundColor Green
    try {
        $ClientProcess = Start-Process -FilePath $PythonExe -ArgumentList "client\main.py" -WindowStyle Normal -PassThru
        $ClientProcesses += $ClientProcess
        Write-Host "Client $i started (PID: $($ClientProcess.Id))" -ForegroundColor Green
        
        if ($i -lt 4) {
            Start-Sleep -Seconds 1
        }
    } catch {
        Write-Host "Failed to start client $i : $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Launch completed!" -ForegroundColor Green
Write-Host "Server: PID $($ServerProcess.Id)" -ForegroundColor White
Write-Host "Clients: $($ClientProcesses.Count) processes" -ForegroundColor White
foreach ($i in 0..($ClientProcesses.Count-1)) {
    Write-Host "  Client $($i+1): PID $($ClientProcesses[$i].Id)" -ForegroundColor White
}
Write-Host "========================================" -ForegroundColor Cyan

# Simple management
Write-Host "Options:" -ForegroundColor Yellow
Write-Host "  'q' - Stop all processes and exit" -ForegroundColor White
Write-Host "  Any other key - Exit script (processes continue)" -ForegroundColor White

$choice = Read-Host "`nYour choice"

if ($choice.ToLower() -eq 'q') {
    Write-Host "Stopping all processes..." -ForegroundColor Red
    
    # Stop server
    if ($ServerProcess -and !$ServerProcess.HasExited) {
        $ServerProcess.Kill()
        Write-Host "Server stopped" -ForegroundColor Green
    }
    
    # Stop clients
    foreach ($client in $ClientProcesses) {
        if ($client -and !$client.HasExited) {
            $client.Kill()
        }
    }
    Write-Host "All clients stopped" -ForegroundColor Green
    Write-Host "All processes stopped!" -ForegroundColor Green
} else {
    Write-Host "Script exiting - processes continue running" -ForegroundColor White
}

Write-Host "Done!" -ForegroundColor Green
