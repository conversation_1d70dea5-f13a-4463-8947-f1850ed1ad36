"""
主线程UI消息中心
负责接收子线程信号并进行分类处理
"""

from PySide6.QtCore import QObject, Signal, QPointF
from typing import Dict, Callable


class Signals(QObject):
    """UIMessageHub的线程信号"""
    ui_signal = Signal(dict)  # 所有 UI主线程 -> 子线程消息
    business_signal = Signal(dict)  # 所有 子线程 -> UI主线程消息
    user_register_response = Signal(dict)  # 用户注册响应信号
    user_login_response = Signal(dict)     # 用户登录响应信号
    room_event_response = Signal(dict)    # 房间事件响应信号

    """UIWindow的信号"""
    scene_click_signal = Signal(str, QPointF)  # 战场场景点击信号


class UIMessageHub(QObject):
    """主线程UI消息中心 - 接收子线程信号并处理"""

    def __init__(self, logger):
        super().__init__()

        self.signals = Signals()
        self.logger = logger
        self.mainwindow = None
        self.logger.info("UIMessageHub 创建完成")

        # 绑定业务线程信号和自身的槽函数
        self.signals.business_signal.connect(self.handle_business_message)
        self.logger.info("业务线程信号连接完成,可以接受子线程消息")

        # 消息处理器字典
        self.message_handlers: Dict[str, Callable] = {
            'user_register_response': self._handle_user_register_response,
            'user_login_response': self._handle_user_login_response,
            'room_event_response': self._handle_room_event_response,
        }


    # ==================== 信号处理方法 ====================

    def handle_business_message(self, message: dict):
        """统一的业务消息处理器 - 接收子线程消息"""
        message_type = message.get("type", "unknown")
        data = message.get("data", {})

        print(f"[UI主线程] 收到子线程消息: {message_type}")
        print(f"[UI主线程] 消息数据: {data}")

        # 根据消息类型分发到具体处理方法（不再检查data是否为空）
        handler = self.message_handlers.get(message_type, self._handle_default_message)
        handler(message)


    # ==================== 具体消息处理方法 ====================

    def _handle_user_register_response(self, message: dict):
        """处理用户注册响应消息"""
        self.signals.user_register_response.emit(message)
        self.logger.info("服务器返回用户注册响应")

    def _handle_user_login_response(self, message: dict):
        """处理用户登录响应消息"""
        self.signals.user_login_response.emit(message)
        self.logger.info("服务器返回用户登录响应")

    def _handle_room_event_response(self, message: dict):
        """处理房间事件响应"""
        self.signals.room_event_response.emit(message)
        self.logger.info("服务器返回房间事件响应")

    def _handle_default_message(self, message: dict):
        """默认的消息处理器"""
        message_type = message.get('type', 'unknown')
        self.logger.warning(f"未找到消息类型 '{message_type}' 的处理器")

    
    # ==================== 公共接口方法 ====================

    def send_to_business_thread(self, message: dict = {}):
        """统一的消息发送方法 - 向业务线程发送消息"""
        if self.signals.ui_signal is None:
            self.logger.warning("UI信号未设置，无法发送消息到业务线程")
            return
        self.logger.info(f"UI主线程发送消息到业务线程: {message['type']}")
        self.signals.ui_signal.emit(message)
