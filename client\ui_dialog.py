from PySide6.QtWidgets import (
    QWidget, QDialog, QVBoxLayout, QHBoxLayout, QFormLayout,
    QLabel, QLineEdit, QPushButton, QMessageBox, QFrame, QApplication
)
from PySide6.QtGui import (
    QIcon, QPixmap, QRegularExpressionValidator, QKeySequence,
    QPainter, QBrush, QColor, QPen
)
from PySide6.QtCore import Qt, QRegularExpression, QTimer
import hashlib


def center_window(win):
    """居中窗口的方法"""
    # 获取主屏幕对象
    screen = QApplication.primaryScreen()
    # 获取屏幕几何信息
    screen_geometry = screen.geometry()
    # 计算窗口居中位置
    new_x = int((screen_geometry.width() - win.geometry().width()) / 2 - 6)
    new_y = int((screen_geometry.height() - win.geometry().height()) / 2 - 40)
    win.move(new_x, new_y)


class Login(QWidget):
    def __init__(self, main_window):
        super(<PERSON>gin, self).__init__()
        self.main_window = main_window
        self.main_window.message_hub.signals.user_login_response.connect(self.login_response)
        self.login_flag = False
        self.init()
        self.main_window.logger.info('初始化登入窗口')

    def init(self):
        self.setWindowTitle("欢迎加入超级四国大战！")
        self.setWindowIcon(QIcon('./client/image/4InWar.ico'))
        self.setWindowFlags(Qt.WindowType.WindowMinimizeButtonHint | Qt.WindowType.WindowCloseButtonHint)
        self.setFixedSize(400, 260)
        center_window(self)
        self.show()

        logo = QLabel()
        logo.setPixmap(QPixmap("./client/image/logo.png"))
        logo.resize(400, 150)
        hbox1 = QHBoxLayout()
        hbox1.addWidget(logo)
        self.name = QLineEdit()
        self.name.setMaximumWidth(180)
        self.pwd = QLineEdit()
        self.pwd.setMaximumWidth(180)
        layout = QFormLayout()
        layout.setFormAlignment(Qt.AlignmentFlag.AlignHCenter)
        layout.addRow('用户名', self.name)
        layout.addRow('密　码', self.pwd)
        self.name.setEchoMode(QLineEdit.EchoMode.Normal)
        self.name.setPlaceholderText("请输入1-8个中文字符")
        self.pwd.setEchoMode(QLineEdit.EchoMode.Password)
        self.pwd.setPlaceholderText("请输入密码")
        n = QRegularExpression(r'[\p{Han}]{1,8}')
        p = QRegularExpression(r'[a-zA-Z0-9]+')
        v1 = QRegularExpressionValidator(self)
        v2 = QRegularExpressionValidator(self)
        v1.setRegularExpression(n)
        v2.setRegularExpression(p)
        self.name.setValidator(v1)
        self.pwd.setValidator(v2)
        self.reg = QPushButton('注册')
        self.log = QPushButton('登入')
        self.log.setFocus()
        self.log.setDefault(True)
        self.log.setShortcut(QKeySequence(Qt.Key.Key_Return))
        self.reg.setMaximumWidth(105)
        self.log.setMaximumWidth(105)
        self.reg.clicked.connect(self.reg_page)
        self.log.clicked.connect(self.login)
        hbox2 = QHBoxLayout()
        hbox2.addSpacing(90)
        hbox2.addWidget(self.reg)
        hbox2.addSpacing(3)
        hbox2.addWidget(self.log)
        hbox2.addSpacing(80)
        vbox = QVBoxLayout()
        vbox.addLayout(hbox1)
        vbox.addSpacing(13)
        vbox.addLayout(layout)
        vbox.addLayout(hbox2)
        vbox.setContentsMargins(0, 0, 0, 10)
        self.setLayout(vbox)

    def reg_page(self):
        self.hide()
        self.name.clear()
        self.pwd.clear()
        self.register = Register(self)
        if self.register.exec():
            pass
        self.show()

    def closeEvent(self, event):
        self.main_window.logger.info('捕获到登入窗口关闭事件')
        if not self.login_flag and self.main_window.login_widget_on:
            event.ignore()
            self.main_window.close()
        else:
            event.accept()

    def login(self, name='', pwd=''):
        if not name and not pwd:
            str_name = self.name.text()
            print(str_name)
            str_pwd = self.pwd.text()
            print(str_pwd)
        else:
            str_name = name
            str_pwd = pwd

        if len(str_name) == 0:
            QMessageBox.warning(self, "登入错误！", "必须输入用户名，请重试。")
            return
        elif len(str_pwd) == 0:
            QMessageBox.warning(self, "登入错误！", "必须输入密码，请重试。")
            return
        self.reg.setEnabled(False)
        self.log.setEnabled(False)
        # 使用SHA256进行客户端哈希（确定性哈希，不使用随机盐）
        import hashlib
        password_hash = hashlib.sha256(str_pwd.encode('utf-8')).hexdigest()
        message = {
            'type': 'user_login',
            'data': {
                'username': str_name,
                'password': password_hash
            }
        }
        self.main_window.message_hub.send_to_business_thread(message)
        print('登入中...')

    def login_response(self, message: dict):
        # 正确提取服务器响应数据
        data = message.get('data', {})
        success = data.get('success', False)
        error = data.get('error', '')

        print(f"[Login] 收到登录响应: success={success}, error={error}")

        if success:
            self.login_flag = True
            self.close()
            self.main_window.show()
            self.main_window.logger.info('登入成功，显示主窗口')
            self.main_window.user_data = data.get('user_data', {})
            self.main_window.user_name_label.setText(f"""
<b style="color: #a06c44; font-size: 15px;">{self.main_window.user_data['username']}</b><br>
""")
            self.main_window.user_info_label.setText(f"""
<p style="font-size: 12px; font-weight: bold; text-align: right;">
UID: {self.main_window.user_data['id']}<br>等级:{self.main_window.user_data["user_level"]}<br></p>
""")

            # 处理房间信息
            room_info = data.get('room_info', {})
            room_locked_seats = data.get('room_locked_seats', {})
            if room_info:
                self.main_window.logger.info(f'收到房间信息，共 {len(room_info)} 个房间')
                self.main_window.load_existing_rooms(room_info, room_locked_seats)
        else:
            # 根据错误信息判断错误类型
            if '用户名' in error or '不存在' in error:
                QMessageBox.warning(self, "登入错误！", "此用户名尚未注册，请先注册后再登入。")
                self.name.setText("")
                self.pwd.setText("")
            elif '禁用' in error:
                QMessageBox.warning(self, "登入错误！", "此账户已被禁用，请联系管理员。")
            elif '密码' in error:
                QMessageBox.warning(self, "登入错误！", "密码错误，请重新输入。")
                self.pwd.setText("")
            else:
                QMessageBox.warning(self, "登入错误！", error)

            self.reg.setEnabled(True)
            self.log.setEnabled(True)


class Register(QDialog):
    def __init__(self, login_widget):
        super().__init__()
        self.logger = login_widget.main_window.logger
        self.login_widget = login_widget
        self.login_widget.main_window.message_hub.signals.user_register_response.connect(self.register_response)
        self.init()

    def init(self):
        self.setWindowTitle("欢迎注册超级四国大战！")
        self.setWindowIcon(QIcon('./client/image/4InWar.ico'))
        self.setWindowFlags(Qt.WindowType.WindowMinimizeButtonHint | Qt.WindowType.WindowCloseButtonHint)
        self.setFixedSize(400, 180)
        center_window(self)
        self.name = QLineEdit()
        self.name.setMaximumWidth(180)
        self.pwd = QLineEdit()
        self.pwd.setMaximumWidth(180)
        self.pwd1 = QLineEdit()
        self.pwd1.setMaximumWidth(180)
        self.invitation = QLineEdit()
        self.invitation.setMaximumWidth(180)
        layout = QFormLayout()
        layout.setFormAlignment(Qt.AlignmentFlag.AlignHCenter)
        layout.addRow('用　户　名', self.name)
        layout.addRow('密　　　码', self.pwd)
        layout.addRow('请确认密码', self.pwd1)
        layout.addRow('邀　请　码', self.invitation)
        self.name.setEchoMode(QLineEdit.EchoMode.Normal)
        self.name.setPlaceholderText("请输入1-8个中文字符")
        self.pwd.setEchoMode(QLineEdit.EchoMode.Password)
        self.pwd.setPlaceholderText("只允许数字和英文")
        self.pwd1.setEchoMode(QLineEdit.EchoMode.Password)
        self.pwd1.setPlaceholderText("只允许数字和英文")
        self.invitation.setEchoMode(QLineEdit.EchoMode.Normal)
        self.invitation.setPlaceholderText("请输入邀请码")
        n = QRegularExpression(r'[\p{Han}]{1,8}')
        p = QRegularExpression(r'[a-zA-Z0-9]+')
        v1 = QRegularExpressionValidator(self)
        v1.setRegularExpression(n)
        v2 = QRegularExpressionValidator(self)
        v2.setRegularExpression(p)
        self.name.setValidator(v1)
        self.pwd.setValidator(v2)
        self.pwd1.setValidator(v2)
        self.reg = QPushButton('注册')
        self.reg.setMaximumWidth(105)
        self.reg.clicked.connect(self.register)
        self.cancel = QPushButton('取消')
        self.cancel.setMaximumWidth(105)
        self.cancel.clicked.connect(self.close)
        hbox = QHBoxLayout()
        hbox.addSpacing(90)
        hbox.addWidget(self.reg)
        hbox.addSpacing(3)
        hbox.addWidget(self.cancel)
        hbox.addSpacing(80)
        vbox = QVBoxLayout()
        vbox.addLayout(layout)
        vbox.addLayout(hbox)
        vbox.setContentsMargins(0, 10, 0, 10)
        self.setLayout(vbox)

    def register(self):
        str_name = self.name.text()
        print(len(str_name))
        str_pwd = self.pwd.text()
        print(str_pwd)
        str_pwd1 = self.pwd1.text()
        print(str_pwd1)
        str_invitation = self.invitation.text()
        print(str_invitation)
        if len(str_name) == 0:
            QMessageBox.warning(self, "注册错误！", "必须设置用户名，请重试。")
            return
        elif len(str_pwd) == 0:
            QMessageBox.warning(self, "注册错误！", "必须设置密码，请重试。")
            return
        elif len(str_pwd1) == 0:
            QMessageBox.warning(self, "注册错误！", "必须输入验证密码，请重试。")
            return
        elif str_pwd != str_pwd1:
            QMessageBox.warning(self, "注册错误！", "两次输入的密码不相同，请重试。")
            self.pwd.setText("")
            self.pwd1.setText("")
            return
        elif len(str_invitation) == 0:
            QMessageBox.warning(self, "注册错误！", "必须输入邀请码，请重试。")
            return
            
        self.reg.setEnabled(False)
        self.cancel.setEnabled(False)
        self.name.setEnabled(False)
        self.pwd.setEnabled(False)
        self.pwd1.setEnabled(False)
        self.invitation.setEnabled(False)
        # 使用SHA256进行客户端哈希（与登录保持一致）
        password_hash = hashlib.sha256(str_pwd.encode('utf-8')).hexdigest()
        message = {
            'type': 'user_register',
            'data': {
                'username': str_name,
                'password': password_hash,
                'invitation': str_invitation
            }
        }
        self.login_widget.main_window.message_hub.send_to_business_thread(message)
        print('注册中...')

    def register_response(self, message: dict):
        self.reg.setEnabled(True)
        self.cancel.setEnabled(True)
        self.name.setEnabled(True)
        self.pwd.setEnabled(True)
        self.pwd1.setEnabled(True)
        self.invitation.setEnabled(True)

        # 正确提取服务器响应数据
        data = message.get('data', {})
        response_type = data.get('type')
        response_message = data.get('message')

        print(f"[Register] 收到注册响应: type={response_type}, message={response_message}")

        if response_type == 'success' and response_message:
            QMessageBox.information(self, "注册成功！", response_message)
            self.close()
        elif response_type and response_message:
            QMessageBox.warning(self, "注册错误！", response_message)
            if response_type == 'wrong_invitation':
                self.invitation.setText("")
            elif response_type == 'same_name':
                self.name.setText("")
                self.invitation.setText("")


class RoomNameDialog(QDialog):
    """房间名称输入对话框"""

    def __init__(self, main_window):
        super().__init__()
        self.main_window = main_window
        self.logger = main_window.logger
        self.room_name = ""
        self.waiting_frame = None
        self.is_waiting = False

        # 超时相关
        self.timeout_timer = QTimer()
        self.countdown_timer = QTimer()
        self.countdown_seconds = 10
        self.countdown_label = None

        # 连接超时信号
        self.timeout_timer.timeout.connect(self.handle_timeout)
        self.countdown_timer.timeout.connect(self.update_countdown)

        self.init_ui()

    def init_ui(self):
        """初始化UI"""
        self.setWindowTitle("创建新房间")
        self.setWindowIcon(QIcon('./client/image/4InWar.ico'))
        self.setWindowFlags(Qt.WindowType.FramelessWindowHint | Qt.WindowType.Dialog)  # 隐藏标题栏
        self.setFixedSize(450, 180)  # 调整窗口尺寸以适应更大的输入框
        center_window(self)

        # 创建输入框
        self.room_name_input = QLineEdit()
        self.room_name_input.setMinimumWidth(375)
        self.room_name_input.setMaximumWidth(375)
        self.room_name_input.setMinimumHeight(35)
        self.room_name_input.setPlaceholderText("请输入房间名称（最多8个汉字）")

        # 使用\p{Han}匹配汉字，添加英文数字和下划线支持
        pattern = QRegularExpression(r'[\p{Han}a-zA-Z0-9_]{8}')
        validator = QRegularExpressionValidator(self)
        validator.setRegularExpression(pattern)
        self.room_name_input.setValidator(validator)
        self.room_name_input.setMaxLength(16)

        # 确保输入框可以获得焦点和输入
        self.room_name_input.setFocusPolicy(Qt.FocusPolicy.StrongFocus)
        self.room_name_input.setEnabled(True)
        self.room_name_input.setReadOnly(False)  # 确保不是只读

        # 设置输入框样式
        self.room_name_input.setStyleSheet("""
            QLineEdit {
                padding: 8px;
                font-size: 14px;
                border: 2px solid #6a6a6a;
                border-radius: 5px;
                background-color: white;
                color: black;
            }
            QLineEdit:focus {
                border: 2px solid #a06c44;
            }
        """)

        # 创建按钮
        self.confirm_btn = QPushButton('确认创建')
        self.cancel_btn = QPushButton('取消')
        self.confirm_btn.setMaximumWidth(100)
        self.cancel_btn.setMaximumWidth(100)

        # 设置按钮样式
        button_style = """
            QPushButton {
                background-color: #808080;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 8px 16px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #a06c44;
            }
            QPushButton:pressed {
                background-color: #8a5a3a;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }
        """
        self.confirm_btn.setStyleSheet(button_style)
        self.cancel_btn.setStyleSheet(button_style)

        # 设置默认按钮和快捷键
        self.confirm_btn.setDefault(True)
        self.confirm_btn.setShortcut(QKeySequence(Qt.Key.Key_Return))
        self.cancel_btn.setShortcut(QKeySequence(Qt.Key.Key_Escape))

        # 连接信号
        self.confirm_btn.clicked.connect(self.confirm_create)
        self.cancel_btn.clicked.connect(self.reject)

        # 设置对话框属性以支持圆角
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground)

        # 设置基础样式
        self.setStyleSheet("""
            QWidget {
                background-color: transparent;
            }
        """)

        # 布局
        main_layout = QVBoxLayout()
        main_layout.setSpacing(10)
        main_layout.setContentsMargins(15, 15, 15, 15)

        # 自定义标题栏
        title_bar = QWidget()
        title_bar.setFixedHeight(40)
        title_bar_layout = QHBoxLayout(title_bar)
        title_bar_layout.setContentsMargins(10, 5, 10, 5)
        title_bar_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)

        # 标题标签
        title_label = QLabel("创建新房间")
        title_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #333333;")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)

        title_bar_layout.addWidget(title_label)

        # 提示标签
        prompt_label = QLabel("请输入房间名称：")
        prompt_label.setStyleSheet("font-size: 14px; font-weight: bold; color: #555555;")

        # 输入框布局
        input_layout = QHBoxLayout()
        input_layout.addStretch()
        input_layout.addWidget(self.room_name_input)
        input_layout.addStretch()

        # 按钮布局
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        button_layout.addWidget(self.cancel_btn)
        button_layout.addSpacing(10)
        button_layout.addWidget(self.confirm_btn)
        button_layout.addStretch()

        # 主布局
        main_layout.addWidget(title_bar)
        main_layout.addWidget(prompt_label)
        main_layout.addLayout(input_layout)
        main_layout.addLayout(button_layout)

        self.setLayout(main_layout)

    def paintEvent(self, event):
        """重写绘制事件，绘制圆角背景"""
        super().paintEvent(event)
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)

        # 绘制圆角背景
        rect = self.rect().adjusted(1, 1, -1, -1)  # 稍微缩小以避免边框被裁切
        painter.setBrush(QBrush(QColor("#f0f0f0")))
        painter.setPen(QPen(QColor("#a06c44"), 2))
        painter.drawRoundedRect(rect, 10, 10)

    def keyPressEvent(self, event):
        """重写键盘事件，在等待状态时阻止ESC键"""
        if self.is_waiting and event.key() == Qt.Key.Key_Escape:
            # 在等待状态时忽略ESC键
            event.ignore()
            return

        # 其他情况正常处理
        super().keyPressEvent(event)

    def showEvent(self, event):
        """重写显示事件，确保输入框获得焦点"""
        super().showEvent(event)
        # 延迟设置焦点，确保窗口完全显示后再设置
        QTimer.singleShot(100, self.room_name_input.setFocus)

    def confirm_create(self):
        """确认创建房间"""
        if self.is_waiting:
            return  # 如果正在等待，忽略点击

        room_name = self.room_name_input.text().strip()

        if not room_name:
            QMessageBox.warning(self, "输入错误", "房间名称不能为空！")
            return

        # 检查字符长度（汉字按2个字符计算）
        char_count = 0
        for char in room_name:
            if '\u4e00' <= char <= '\u9fa5':  # 汉字
                char_count += 2
            else:  # 其他字符
                char_count += 1

        if char_count > 16:  # 8个汉字 = 16个字符
            QMessageBox.warning(self, "输入错误", "房间名称过长！最多8个汉字或16个字符。")
            return

        # 保存房间名称并进入等待状态
        self.room_name = room_name
        self.enter_waiting_state()

        # 发送创建房间请求
        message = {
            'type': 'room_event',
            'data': {
                'type': 'create',
                'room_name': room_name,
                'username': self.main_window.user_data['username']
            }
        }
        self.main_window.message_hub.send_to_business_thread(message)
        self.logger.info(f'发送创建房间请求: {room_name}')

    def reject(self):
        """重写reject方法，在等待状态时阻止关闭"""
        if self.is_waiting:
            # 在等待状态时不允许关闭
            return
        super().reject()

    def enter_waiting_state(self):
        """进入等待状态"""
        self.is_waiting = True

        # 禁用所有控件
        self.room_name_input.setEnabled(False)
        self.confirm_btn.setEnabled(False)
        self.cancel_btn.setEnabled(False)

        # 重置倒计时
        self.countdown_seconds = 10

        # 创建等待提示框
        self.create_waiting_frame()

        # 启动超时定时器（10秒）
        self.timeout_timer.start(10000)  # 10秒超时

        # 启动倒计时定时器（每秒更新）
        self.countdown_timer.start(1000)  # 每秒更新一次

    def create_waiting_frame(self):
        """创建等待提示框"""
        if self.waiting_frame:
            return

        # 创建半透明背景
        self.waiting_frame = QFrame(self)
        self.waiting_frame.setStyleSheet("""
            QFrame {
                background-color: rgba(0, 0, 0, 150);
                border-radius: 10px;
            }
        """)

        # 设置位置和大小（居中显示）
        dialog_size = self.size()
        frame_width = 250  # 稍微加宽等待框
        frame_height = 80
        x = (dialog_size.width() - frame_width) // 2
        y = (dialog_size.height() - frame_height) // 2
        self.waiting_frame.setGeometry(x, y, frame_width, frame_height)

        # 创建布局和标签
        layout = QVBoxLayout(self.waiting_frame)
        layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.setSpacing(10)

        waiting_label = QLabel("等待服务器响应...")
        waiting_label.setStyleSheet("""
            color: white;
            font-size: 14px;
            font-weight: bold;
            background-color: transparent;
        """)
        waiting_label.setAlignment(Qt.AlignmentFlag.AlignCenter)

        # 倒计时标签
        self.countdown_label = QLabel(f"超时倒计时: {self.countdown_seconds}秒")
        self.countdown_label.setStyleSheet("""
            color: #ffaa00;
            font-size: 12px;
            font-weight: bold;
            background-color: transparent;
        """)
        self.countdown_label.setAlignment(Qt.AlignmentFlag.AlignCenter)

        layout.addWidget(waiting_label)
        layout.addWidget(self.countdown_label)
        self.waiting_frame.show()

    def exit_waiting_state(self):
        """退出等待状态"""
        self.is_waiting = False

        # 停止定时器
        self.timeout_timer.stop()
        self.countdown_timer.stop()

        # 移除等待提示框
        if self.waiting_frame:
            self.waiting_frame.hide()
            self.waiting_frame.deleteLater()
            self.waiting_frame = None
            self.countdown_label = None

        # 重新启用控件
        self.room_name_input.setEnabled(True)
        self.confirm_btn.setEnabled(True)
        self.cancel_btn.setEnabled(True)

        # 设置焦点到输入框
        self.room_name_input.setFocus()

    def update_countdown(self):
        """更新倒计时显示"""
        self.countdown_seconds -= 1
        if self.countdown_label:
            self.countdown_label.setText(f"超时倒计时: {self.countdown_seconds}秒")

        # 当倒计时接近结束时，改变颜色警告
        if self.countdown_seconds <= 3 and self.countdown_label:
            self.countdown_label.setStyleSheet("""
                color: #ff4444;
                font-size: 12px;
                font-weight: bold;
                background-color: transparent;
            """)

    def handle_timeout(self):
        """处理超时"""
        self.logger.warning('创建房间请求超时')

        # 停止定时器
        self.timeout_timer.stop()
        self.countdown_timer.stop()

        # 退出等待状态，恢复到可操作状态
        self.exit_waiting_state()

        # 显示超时提示，询问用户是否重试
        reply = QMessageBox.question(self, "请求超时",
                                   "服务器响应超时，请检查网络连接。\n\n"
                                   "是否要重试创建房间？",
                                   QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                                   QMessageBox.StandardButton.Yes)

        if reply == QMessageBox.StandardButton.No:
            # 用户选择不重试，关闭对话框
            self.reject()
        # 如果用户选择重试，保持对话框打开，用户可以重新输入房间名称

    def closeEvent(self, event):
        """重写关闭事件"""
        if self.is_waiting:
            # 如果正在等待，不允许关闭
            event.ignore()
        else:
            # 停止定时器
            self.timeout_timer.stop()
            self.countdown_timer.stop()
            event.accept()
