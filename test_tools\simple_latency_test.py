#!/usr/bin/env python3
"""
简单的延时测试 - 单次连接测试
"""

import asyncio
import websockets
import json
import time


async def test_single_message():
    """测试单个消息的延时"""
    server_url = "ws://127.0.0.1:8080/ws"
    
    print("🎯 4inWar 简单延时测试")
    print("=" * 50)
    
    try:
        # 连接服务器
        print(f"🔗 连接服务器: {server_url}")
        connect_start = time.time()
        
        async with websockets.connect(server_url) as websocket:
            connect_time = (time.time() - connect_start) * 1000
            print(f"✅ 连接成功，耗时: {connect_time:.2f}ms")
            
            # 发送test消息
            test_message = {
                "type": "test",
                "data": {
                    "content": "简单延时测试消息",
                    "timestamp": time.time()
                }
            }
            
            print("📤 发送test消息...")
            send_time = time.time()
            
            await websocket.send(json.dumps(test_message))
            
            # 接收响应
            response_raw = await websocket.recv()
            receive_time = time.time()
            
            # 计算延时
            latency = (receive_time - send_time) * 1000
            
            # 解析响应
            response = json.loads(response_raw)
            
            print(f"📥 收到响应，延时: {latency:.2f}ms")
            print(f"   响应类型: {response.get('type')}")
            print(f"   服务器消息: {response.get('data', {}).get('server_message', 'N/A')}")
            print(f"   处理内容: {response.get('data', {}).get('processed_content', 'N/A')}")
            
            # 数据库状态
            db_status = response.get('data', {}).get('database_status', 'N/A')
            print(f"   数据库状态: {db_status}")
            
            print("\n🎉 测试完成！")
            print(f"📊 总延时: {latency:.2f}ms")
            print(f"📊 连接时间: {connect_time:.2f}ms")
            print(f"📊 消息处理时间: {latency:.2f}ms")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")


async def test_multiple_messages(count=5):
    """测试多个消息的延时"""
    server_url = "ws://127.0.0.1:8080/ws"
    
    print(f"\n🚀 多消息延时测试 ({count}次)")
    print("=" * 50)
    
    latencies = []
    
    try:
        async with websockets.connect(server_url) as websocket:
            print("✅ 连接成功")
            
            for i in range(count):
                test_message = {
                    "type": "test",
                    "data": {
                        "content": f"测试消息 #{i+1}",
                        "test_id": i+1
                    }
                }
                
                send_time = time.time()
                await websocket.send(json.dumps(test_message))
                
                response_raw = await websocket.recv()
                receive_time = time.time()
                
                latency = (receive_time - send_time) * 1000
                latencies.append(latency)
                
                print(f"📤📥 消息#{i+1}: {latency:.2f}ms")
                
                # 短暂延时避免过快发送
                await asyncio.sleep(0.1)
            
            # 统计结果
            print(f"\n📊 统计结果:")
            print(f"   最小延时: {min(latencies):.2f}ms")
            print(f"   最大延时: {max(latencies):.2f}ms")
            print(f"   平均延时: {sum(latencies)/len(latencies):.2f}ms")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")


async def main():
    """主函数"""
    # 单次测试
    await test_single_message()
    
    # 等待一下
    await asyncio.sleep(2)
    
    # 多次测试
    await test_multiple_messages(5)


if __name__ == "__main__":
    asyncio.run(main())
