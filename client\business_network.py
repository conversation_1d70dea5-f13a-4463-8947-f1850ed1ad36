"""
业务网络通信模块
基于aiohttp websocket的客户端网络通信，运行在业务线程的事件循环中
"""

import aiohttp
import json
from typing import Optional, Dict, Any


class BusinessNetwork:
    """业务网络通信管理器
    
    负责与服务器的websocket连接管理、消息收发处理
    运行在业务线程的异步事件循环中
    """
    
    def __init__(self, logger, business_message_hub, server_url: str = "ws://127.0.0.1:8080/ws"):
        """初始化业务网络通信管理器
        
        Args:
            logger: 日志记录器实例
            business_message_hub: 业务消息处理中心实例，用于路由接收到的消息
            server_url: 服务器WebSocket地址
        """
        self.logger = logger
        self.business_message_hub = business_message_hub
        self.server_url = server_url
        
        # 连接相关
        self.websocket: Optional[aiohttp.ClientWebSocketResponse] = None
        self.session: Optional[aiohttp.ClientSession] = None
        self.is_running = False
        self.is_connected = False
        
        self.logger.info(f"BusinessNetwork 初始化完成，服务器地址: {server_url}")
    
    async def start(self):
        """启动网络通信模块"""
        if self.is_running:
            self.logger.warning("BusinessNetwork 已在运行")
            return
        
        self.is_running = True
        self.logger.info("启动 BusinessNetwork")
        
        try:
            # 创建客户端会话
            self.session = aiohttp.ClientSession()
            
            # 连接服务器
            await self.connect()
            
            # 启动消息接收循环
            if self.is_connected:
                await self._message_receive_loop()
            
        except Exception as e:
            self.logger.error(f"BusinessNetwork 启动异常: {e}")
            raise
        finally:
            await self._cleanup()
            self.is_running = False
            self.logger.info("BusinessNetwork 已停止")
    
    async def connect(self):
        """连接到服务器"""
        try:
            self.logger.info(f"正在连接服务器: {self.server_url}")

            # 如果会话不存在，创建一个
            if self.session is None:
                self.session = aiohttp.ClientSession()
                self.logger.debug("创建了新的客户端会话")

            # 建立WebSocket连接
            self.websocket = await self.session.ws_connect(self.server_url)
            self.is_connected = True

            self.logger.info("✅ 服务器连接成功")

        except Exception as e:
            self.logger.error(f"❌ 连接服务器失败: {e}")
            self.is_connected = False
            raise
    
    async def disconnect(self):
        """断开服务器连接"""
        if self.websocket and not self.websocket.closed:
            try:
                await self.websocket.close()
                self.logger.info("WebSocket连接已关闭")
            except Exception as e:
                self.logger.error(f"关闭WebSocket连接时出错: {e}")
        
        self.is_connected = False
    
    async def send_message(self, message: Dict[str, Any]):
        """发送消息到服务器
        
        Args:
            message: 消息字典，格式: {"type": "message_type", "data": {}}
        """
        if not self.is_connected or not self.websocket:
            self.logger.error("❌ 无法发送消息：未连接到服务器")
            return False
        
        try:
            # 验证消息格式
            if not isinstance(message, dict) or 'type' not in message:
                self.logger.error(f"❌ 消息格式错误: {message}")
                return False
            
            # 确保data字段存在
            if 'data' not in message:
                message['data'] = {}
            
            # 发送消息
            message_json = json.dumps(message, ensure_ascii=False)
            await self.websocket.send_str(message_json)
            
            message_type = message.get('type', 'unknown')
            self.logger.info(f"📤 发送消息到服务器: {message_type}")
            self.logger.debug(f"📤 消息内容: {message_json}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 发送消息异常: {e}")
            return False
    
    async def _message_receive_loop(self):
        """消息接收循环
        
        持续接收服务器消息并路由到business_message_hub处理
        """
        self.logger.info("开始消息接收循环")
        
        try:
            if self.websocket is None:
                self.logger.error("❌ 无法接收消息：WebSocket连接不存在")
                return
            async for msg in self.websocket:
                if not self.is_running:
                    break
                
                if msg.type == aiohttp.WSMsgType.TEXT:
                    await self._handle_received_message(msg.data)
                    
                elif msg.type == aiohttp.WSMsgType.ERROR:
                    self.logger.error(f"WebSocket连接错误: {self.websocket.exception()}")
                    break
                    
                elif msg.type == aiohttp.WSMsgType.CLOSE:
                    self.logger.info("服务器关闭了连接")
                    break
                    
        except Exception as e:
            self.logger.error(f"❌ 消息接收循环异常: {e}")
        finally:
            self.is_connected = False
            self.logger.info("消息接收循环已结束")
    
    async def _handle_received_message(self, message_data: str):
        """处理接收到的消息
        
        Args:
            message_data: 接收到的JSON字符串消息
        """
        try:
            # 解析JSON消息
            message = json.loads(message_data)
            message_type = message.get('type', 'unknown')
            
            self.logger.info(f"📥 收到服务器消息: {message_type}")
            self.logger.debug(f"📥 消息内容: {message_data}")
            
            # 路由消息到business_message_hub处理
            if self.business_message_hub:
                await self.business_message_hub.handle_message(message)
            else:
                self.logger.warning("business_message_hub 未设置，无法路由消息")
                
        except json.JSONDecodeError as e:
            self.logger.error(f"❌ 消息JSON解析失败: {e}")
            self.logger.error(f"❌ 原始消息: {message_data}")
        except Exception as e:
            self.logger.error(f"❌ 处理接收消息异常: {e}")
    
    async def stop(self):
        """停止网络通信模块"""
        self.logger.info("正在停止 BusinessNetwork...")
        self.is_running = False
        
        # 断开连接
        await self.disconnect()
    
    async def _cleanup(self):
        """清理资源"""
        try:
            # 关闭WebSocket连接
            await self.disconnect()
            
            # 关闭客户端会话
            if self.session and not self.session.closed:
                await self.session.close()
                self.logger.info("客户端会话已关闭")
                
        except Exception as e:
            self.logger.error(f"清理资源时出错: {e}")
    
    def get_connection_status(self) -> Dict[str, Any]:
        """获取连接状态信息
        
        Returns:
            包含连接状态的字典
        """
        return {
            'is_running': self.is_running,
            'is_connected': self.is_connected,
            'server_url': self.server_url,
            'websocket_closed': self.websocket.closed if self.websocket else True
        }
